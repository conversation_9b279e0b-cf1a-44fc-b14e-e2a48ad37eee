<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.manage.web.mapper.manage.StockShipmentItemDetailMapper">
    <select id="select" parameterType="java.lang.Integer" resultType="net.summerfarm.wms.manage.model.domain.StockAllocationItemDetail">
        /*FORCE_MASTER*/ SELECT s.purchase_no purchaseNo,
        sum(s.actual_out_quantity) actualOutQuantity,
        s.quality_date             qualityDate,
        s.production_date          productionDate,
        s.cabinet_code cabinetCode
        FROM  stock_shipment_item_detail s
        WHERE stock_shipment_item_id = #{stockShipmentItemId}
        group by s.purchase_no, s.quality_date, s.production_date, s.cabinet_code
    </select>

    <select id="selectByItemId" resultType="net.summerfarm.wms.manage.model.domain.StockShipmentItemDetail">
        /*FORCE_MASTER*/ SELECT s.purchase_no purchaseNo, s.actual_out_quantity actualOutQuantity, s.quality_date qualityDate, s.production_date productionDate,s.gl_no glNo
             , s.cabinet_code as cabinetCode
        FROM  stock_shipment_item_detail s
                  LEFT  JOIN stock_shipment_item t on t.id=s.stock_shipment_item_id
        WHERE t.id = #{stockShipmentItemId}
    </select>

    <select id="selectOne" resultType="net.summerfarm.wms.manage.model.domain.StockShipmentItemDetail">
        /*FORCE_MASTER*/ SELECT
        id, stock_shipment_item_id stockShipmentItemId, purchase_no purchaseNo, actual_out_quantity actualOutQuantity, quality_date qualityDate,
        production_date productionDate,gl_no glNo, cabinet_code cabinetCode
        FROM stock_shipment_item_detail
        <where>
            <if test="purchaseNo != null">
                and purchase_no = #{purchaseNo}
            </if>
            <if test="stockShipmentItemId != null">
                and stock_shipment_item_id = #{stockShipmentItemId}
            </if>
            <if test="glNo != null">
                and gl_no = #{glNo}
            </if>
            <if test="qualityDate != null">
                AND quality_date = #{qualityDate}
            </if>
            <if test="produceDate != null">
                AND production_date = #{produceDate}
            </if>
            <if test="cabinetCode != null">
                and cabinet_code = #{cabinetCode}
            </if>
        </where>
    </select>

    <update id="updateOutQuantityAdd">
        update stock_shipment_item_detail
        <set>
            <if test="actualOutQuantityAdd != null">
                actual_out_quantity = ifnull(actual_out_quantity, 0) + #{actualOutQuantityAdd},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insertBatch">
        insert into stock_shipment_item_detail (stock_shipment_item_id, actual_out_quantity , purchase_no, quality_date,production_date,gl_no , create_time
        , cabinet_code)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.stockShipmentItemId}, #{item.actualOutQuantity}, #{item.purchaseNo}, #{item.qualityDate}, #{item.productionDate}, #{item.glNo}, now()
            , #{item.cabinetCode})
        </foreach>
    </insert>
</mapper>