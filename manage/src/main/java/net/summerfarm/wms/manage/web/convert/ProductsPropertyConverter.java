package net.summerfarm.wms.manage.web.convert;

import net.summerfarm.wms.facade.goods.dto.ProductsPropertyDTO;
import net.summerfarm.wms.facade.goods.dto.ProductsPropertyValueDTO;
import net.summerfarm.wms.manage.model.domain.ProductsProperty;
import net.summerfarm.wms.manage.model.domain.ProductsPropertyValue;

/**
 * Description: 转换类<br/>
 * date: 2024/7/16 16:52<br/>
 *
 * <AUTHOR> />
 */
public class ProductsPropertyConverter {

    public static ProductsProperty dto2ProductsProperty(ProductsPropertyDTO dto) {
        if(dto == null){
            return null;
        }
        ProductsProperty productsProperty = new ProductsProperty();

        productsProperty.setId(dto.getId());
        productsProperty.setName(dto.getName());
        productsProperty.setType(dto.getType());
        productsProperty.setFormatType(dto.getFormatType());
        productsProperty.setFormatStr(dto.getFormatStr());
        productsProperty.setStatus(dto.getStatus());
        productsProperty.setCreator(dto.getCreator());
        productsProperty.setCreateTime(dto.getCreateTime());
        productsProperty.setMappingId(dto.getMappingId());

        return productsProperty;
    }

    public static ProductsPropertyValue dto2ProductsPropertyValue(ProductsPropertyValueDTO dto) {
        if(dto == null){
            return null;
        }
        ProductsPropertyValue productsPropertyValue = new ProductsPropertyValue();

        productsPropertyValue.setPdId(dto.getPdId());
        productsPropertyValue.setSku(dto.getSku());
        productsPropertyValue.setProductsPropertyId(dto.getProductsPropertyId());
        productsPropertyValue.setProductsPropertyValue(dto.getProductsPropertyValue());
        productsPropertyValue.setCreator(dto.getCreator());
        productsPropertyValue.setCreateTime(dto.getCreateTime());
        productsPropertyValue.setUpdateTime(dto.getUpdateTime());

        return productsPropertyValue;
    }
}
