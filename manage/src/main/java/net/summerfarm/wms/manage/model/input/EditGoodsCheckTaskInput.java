package net.summerfarm.wms.manage.model.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: dongcheng
 * @date: 2023/8/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EditGoodsCheckTaskInput implements Serializable {

    private static final long serialVersionUID = -1815481060608715950L;
    /**
     * 任务编号
     */
    @NotNull(message = "任务编号不能为空")
    private Long id;
    /**
     * 长度
     */
    @NotNull(message = "长度不能为空")
    private BigDecimal length;
    /**
     * 宽
     */
    @NotNull(message = "宽度不能为空")
    private BigDecimal width;
    /**
     * 高
     */
    @NotNull(message = "高度不能为空")
    private BigDecimal high;
    /**
     * 重量
     */
    @NotNull(message = "重量不能为空")
    private BigDecimal weightNum;
    /**
     * 推送状态
     */
    @NotNull(message = "推送状态不能为空")
    private Integer pushStatus;
    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 存储区域
     */
    private Integer storageLocation;

    /**
     * 规格，包装
     */
    String unit;

    /**
     * 是否国产（0：不是，1：是）
     */
    Integer isDomestic;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期时长单位（day，month）
     */
    private String qualityTimeUnit;

    /**
     * 保质期时长类型, 0 固定时长, 1 到期时间
     */
    private Integer qualityTimeType;

    /**
     * 上传图片url
     */
    private String picUrl;
}