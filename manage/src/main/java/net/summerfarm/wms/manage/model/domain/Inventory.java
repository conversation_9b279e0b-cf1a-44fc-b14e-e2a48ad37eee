package net.summerfarm.wms.manage.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.util.validation.annotation.CharLength;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Inventory implements Serializable {

    private static final long serialVersionUID = -5341202188772830790L;
    @ApiModelProperty(value = "id")
    @Null(message = "invertory.invId", groups = {Add.class})
    @NotNull(message = "invertory.aitId",groups = {Update.class})
    private Long invId;

    @ApiModelProperty(value = "sku编号")
    @Null(message = "invertory.invId", groups = {Add.class})
    @NotNull(message = "invertory.aitId.null", groups = {Update.class})
    private String sku;

    @ApiModelProperty(value = "属性ID")
    private Integer aitId;

    @ApiModelProperty(value = "pdId")
    @NotNull(message = "pdId.null", groups = {Add.class})
    private Long pdId;

    @ApiModelProperty(value = "销售模式")
    private Integer salesMode; //销售模式

    @ApiModelProperty(value = "产地")
    private String origin;  //产地

    @ApiModelProperty(value = "包装")
    @NotNull(message = "包装不能为空", groups = {Add.class,Update.class})
    private String unit;    //包装

    @ApiModelProperty(value = "包数")
    private String pack;

    @ApiModelProperty(value = "租户ID：1-鲜沐")
    private Long tenantId;

    @ApiModelProperty(value = "上新类型：0、平台 1、大客户 2、帆台代仓")
    private String createType;

    @ApiModelProperty(value = "规格")
    @NotNull(message = "weight.null", groups = {Add.class,Update.class})
    @CharLength(max = 50,groups = {Add.class,Update.class},message = "weight.length")
    private String weight; //规格

    @ApiModelProperty(value = "规格备注")
    private String weightNotes;

    @ApiModelProperty(value = "是否为国产，0：不是，1是")
    private Integer isDomestic;

    @ApiModelProperty(value = "体积")
    private String volume;

    @ApiModelProperty(value = "重量")
    @Min(value = 0, message = "重量必须大于0", groups = {Add.class, Update.class})
    private BigDecimal weightNum;

    @ApiModelProperty(value = "有效期")
    private String expiryDate;  //有效期

    @ApiModelProperty(value = "是否展示")
    private Boolean show;

    @ApiModelProperty(value = "生熟度")
    private String maturity;    //

    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    @ApiModelProperty(value = "贮存区域")
    private String storageMethod;

    @ApiModelProperty(value = "销售价")
    @Min(value = 0, message = "invertory.too.min", groups = {Add.class, Update.class})
    private BigDecimal salePrice;

    @ApiModelProperty(value = "促销价")
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "商品介绍")
    private String introduction;

    @ApiModelProperty(value = "标记位-过时的sku")
    private Integer outdated;

    @ApiModelProperty(value = "售后最大数量")
    @Min(value = 1, message = "最大售后量不能小于1", groups = {Add.class, Update.class})
    private Integer afterSaleQuantity;

    @ApiModelProperty(value = "最小起售量")
    @Min(value = 1, message = "最小起售量不能小于1", groups = {Add.class, Update.class})
    private Integer baseSaleQuantity;

    @ApiModelProperty(value = "售卖规格")
    @Min(value = 1, message = "起售规格不能小于1", groups = {Add.class, Update.class})
    private Integer baseSaleUnit;

    @ApiModelProperty(value = "类型 0 自营 1 代仓")
    private Integer type;

    @ApiModelProperty(value = "所属/大客户ID")
    private Integer adminId;

    @ApiModelProperty(value = "是否放入样品池 ")
    @NotNull(message = "是否放入样品池不能为空", groups = {Add.class,Update.class})
    private Integer samplePool;

    @ApiModelProperty(value = "sku头图")
    private String skuPic;

    @ApiModelProperty(value = "售后单位")
    @NotNull(message = "售后单位不能为空", groups = {Add.class,Update.class})
    private String afterSaleUnit;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "供应商是否可见：0不可见，1可见")
    private Integer supplierVisible;

    /**
     * 上新审核状态：0、待审核 1、审核通过 2、审核失败
     */
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;


    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * sku性质(扩展类型)：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     * @see net.summerfarm.enums.InventoryExtTypeEnum#NORMAL
     * @see net.summerfarm.enums.InventoryExtTypeEnum#ACTIVITY
     * @see net.summerfarm.enums.InventoryExtTypeEnum#TEMPORARY_INSURANCE
     * @see net.summerfarm.enums.InventoryExtTypeEnum#UNPACKING
     * @see net.summerfarm.enums.InventoryExtTypeEnum#NOT_SALE
     * @see net.summerfarm.enums.InventoryExtTypeEnum#BROKEN_BAG
     */
    private Integer extType;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 任务类型：0、SPU 1、SKU
     */
    private Integer taskType;

    /**
     * 工商名称
     */
    private String realName;

    /**
     * 审核人adminId
     */
    private Integer auditor;

    /**
     * 0、不展示平均价 1、展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 拒绝原因
     */
    private String refuseReason;

}
