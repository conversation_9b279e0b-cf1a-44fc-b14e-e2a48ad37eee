package net.summerfarm.wms.api.h5.roadsale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * @Date 2023/9/14 10:53
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoadSaleRejectCommand {
    /**
     * 外部单号
     */
    @NotBlank(message = "外部订单号不能为空")
    private String outOrderNo;
    /**
     * 单据类型
     */
    @NotNull(message = "外部订单类型不能为空")
    private Integer outOrderType;
    /**
     * 库存仓
     */
    @NotNull(message = "仓库编号不能为空")
    private Integer warehouseNo;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 明细
     */
    @Valid
    @NotEmpty(message = "拒收明细列表不能为空")
    private List<RoadSaleRejectItemCommand> itemCommandList;

}
