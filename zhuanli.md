【说明书摘要】
本发明提供一种支持截单前售后的提前波次作业方法和系统，属于数据处理技术领域，具体包括：拣货处理模块负责将仓库提前波次生成的出库任务，根据仓库事先配置好的库区规则生成具体的仓库拣货任务供作业人员实施拣货，仓库处理模块负责依托于出库时长内的订单售后明细、异常通知单明细、提前波次任务明细，交叉比对已作业部分和已发起售后部分明细，生成多出入库任务，供仓库感知需拦截、反向入库部分，反向入库完成库存平冲，提升了售后处理的效率。



【权利要求书】
1、一种支持截单前售后的提前波次作业系统，其特征在于，具体包括：
接单处理模块，出库管理模块，任务生成模块，拣货处理模块，仓库处理模块；
其中所述接单处理模块负责面向不同来源、不同渠道订单统一接收存储，用于后续出库、入库作业单据生成来源；
所述出库管理模块负责依托出库任务生成策略，生成相应出库任务，并包含多个出库执行功能；
所述任务生成模块负责依托于将接收到的订单转换成仓库内部通知单，由任务调度中心根据波次配置策略周期性调度待履约通知单生成提前波次任务，供仓库人员获取单据进行仓库作业；
所述拣货处理模块负责将仓库提前波次生成的出库任务，根据仓库事先配置好的库区规则生成具体的仓库拣货任务供作业人员实施拣货；
所述仓库处理模块负责依托于出库时长内的订单售后明细、异常通知单明细、提前波次任务明细，交叉比对已作业部分和已发起售后部分明细，生成多出入库任务，供仓库感知需拦截、反向入库部分，反向入库完成库存平冲。
2、如权利要求1所述的支持截单前售后的提前波次作业系统，其特征在于，所述出库执行功能包括仓库库存锁定、拣货任务生成、确认出库、完成出库。
3、如权利要求1所述的支持截单前售后的提前波次作业方法，其特征在于，还包括售后作业补偿模块，依托于订单中心履约推送售后单到仓储系统统一接单服务转换生成仓库内部异常通知单，反向生成多出入库任务，在仓库出库完成交接前推送多出入库指令，拦截已在作业的已发起售后的出库实物部分，反向入库完成库存平冲。
4、一种支持截单前售后的提前波次作业方法，应用于权利要求1-3任一项所述的一种支持截单前售后的提前波次作业系统，其特征在于，具体包括：
实时获取订单对应的商品，并以所述商品对应的历史销售量以及所述商品的库存情况，确定所述商品的出库处理时长；
将存在所述商品的订单作为匹配商品订单以及匹配用户，确定所述商品与所述匹配用户的历史购物数据的匹配情况，利用所述匹配情况确定所述商品与匹配用户的匹配系数，利用所述匹配系数确定所述商品存在匹配偏差用户时，进入下一步骤；
基于与不同的匹配偏差用户的匹配系数，确定所述商品的匹配偏差系数存在异常时，进入下一步骤；
获取仓库在预设时段内的出库数据，并结合所述匹配偏差系数对所述出库处理时长进行修正得到修正处理时长，根据修正处理时长进行所述商品的出库时长的确定，利用所述出库时长内的售后数据进行提前拦截，并对所述商品进行库存更新。
5、如权利要求4所述的支持截单前售后的提前波次作业方法，其特征在于，所述商品对应的历史销售量根据所述商品在购物平台的销售数据的解析结果进行确定。
6、如权利要求4所述的支持截单前售后的提前波次作业方法，其特征在于，所述商品的库存情况根据所述商品的库存量进行确定，具体的根据所述仓库的库存系统的解析结果进行确定。
7、如权利要求4所述的支持截单前售后的提前波次作业方法，其特征在于，所述商品的出库处理时长的确定的方法为：
以所述商品对应的历史销售量确定所述商品的日均销售量；
根据所述商品的库存情况确定所述商品的库存量，并利用所述商品的日均销售量确定所述商品的可用销售时长；
基于所述商品的可用销售时长确定所述商品的出库处理时长。
8、如权利要求4所述的支持截单前售后的提前波次作业方法，其特征在于，确定所述商品的匹配偏差系数存在异常，具体包括：
以与不同的匹配偏差用户的匹配系数为基础，确定不同的匹配偏差用户的权重系数；
根据不同的匹配偏差用户的权重系数确定所述商品的匹配偏差系数，并结合所述预设系数阈值确定所述商品的匹配偏差系数是否存在异常。
9、如权利要求8所述的支持截单前售后的提前波次作业方法，其特征在于，当所述商品的匹配偏差系数小于预设系数阈值时，则确定所述商品的匹配偏差系数存在异常。 

【说明书】
一种支持截单前售后的提前波次作业方法和系统
技术领域
本发明属于数据处理技术领域，尤其涉及 一种支持截单前售后的提前波次作业方法和系统。
背景技术
在进行商品的发货过程中，不可避免的会存在退货等问题的出现，因此为了实现对商品发货过程中的截单处理，在发明专利申请CN201610174149.0《订单信息处理方法、装置及系统》中通过监听到针对指定交易订单的截单通知消息时，判断该指定交易订单关联的仓储作业订单的状态，并向对应配送服务方的运输管理模块及时发送拦截指令，但是却存在以下技术问题：
在进行拦截处理时，现有技术方案中忽视了在仓库端就提前进行拦截处理，并针对性的利用拦截订单进行针对性的进行库存的调整，从而无法在降低售后成本的基础上，提升库存的管理的灵活性。
针对上述技术问题，本发明提供了一种支持截单前售后的提前波次作业方法和系统。
发明内容
为实现本发明目的，本发明采用如下技术方案：
根据本发明的一个方面，提供了一种支持截单前售后的提前波次作业方法和系统。
一种支持截单前售后的提前波次作业系统，具体包括：
接单处理模块，出库管理模块，任务生成模块，拣货处理模块，仓库处理模块；
其中所述接单处理模块负责面向不同来源、不同渠道订单统一接收存储，用于后续出库、入库作业单据生成来源；
所述出库管理模块负责依托出库任务生成策略，生成相应出库任务，并包含多个出库执行功能；
所述任务生成模块负责依托于将接收到的订单转换成仓库内部通知单，由任务调度中心根据波次配置策略周期性调度待履约通知单生成提前波次任务，供仓库人员获取单据进行仓库作业；
所述拣货处理模块负责将仓库提前波次生成的出库任务，根据仓库事先配置好的库区规则生成具体的仓库拣货任务供作业人员实施拣货；
所述仓库处理模块负责依托于出库时长内的订单售后明细、异常通知单明细、提前波次任务明细，交叉比对已作业部分和已发起售后部分明细，生成多出入库任务，供仓库感知需拦截、反向入库部分，反向入库完成库存平冲。
进一步的技术方案在于，所述出库执行功能包括仓库库存锁定、拣货任务生成、确认出库、完成出库。
进一步的技术方案在于，还包括售后作业补偿模块，依托于订单中心履约推送售后单到仓储系统统一接单服务转换生成仓库内部异常通知单，反向生成多出入库任务，在仓库出库完成交接前推送多出入库指令，拦截已在作业的已发起售后的出库实物部分，反向入库完成库存平冲。
另一方面，本申请提供一种支持截单前售后的提前波次作业方法，应用于上述的一种支持截单前售后的提前波次作业系统，具体包括：
S1实时获取订单对应的商品，并以所述商品对应的历史销售量以及所述商品的库存情况，确定所述商品的出库处理时长；
S2将存在所述商品的订单作为匹配商品订单以及匹配用户，确定所述商品与所述匹配用户的历史购物数据的匹配情况，利用所述匹配情况确定所述商品与匹配用户的匹配系数，利用所述匹配系数确定所述商品存在匹配偏差用户时，进入下一步骤；
S3基于与不同的匹配偏差用户的匹配系数，确定所述商品的匹配偏差系数存在异常时，进入下一步骤；
S4获取仓库在预设时段内的出库数据，并结合所述匹配偏差系数对所述出库处理时长进行修正得到修正处理时长，根据修正处理时长进行所述商品的出库时长的确定，利用所述出库时长内的售后数据进行提前拦截，并对所述商品进行库存更新。
本发明的有益效果在于：
以仓库在预设时段内的出库数据、匹配偏差系数对出库处理时长进行修正得到修正处理时长，既考虑到仓库在预设时段内的出库数据的繁忙程度对商品的出库处理时长的影响，避免了由于出库量较少的情况下导致的批量出库状态下的出库处理用时较长的技术问题的出现，同时进一步结合匹配偏差情况，实现了对售后概率较高的商品的出库处理时长的动态调整，也为降低售后成本奠定了基础。
利用出库时长内的售后数据进行提前拦截，并对商品进行库存更新，从而实现了在售后端进行商品的提前拦截以及库存更新，降低了售后处理的成本，并通过进一步对商品的库存进行更新，提升了商品的售后和售卖处理的灵活性，实现了仓库的优化管理。
进一步的技术方案在于，所述商品对应的历史销售量根据所述商品在购物平台的销售数据的解析结果进行确定。
进一步的技术方案在于，所述商品的库存情况根据所述商品的库存量进行确定，具体的根据所述仓库的库存系统的解析结果进行确定。
进一步的技术方案在于，所述商品的出库处理时长的确定的方法为：
以所述商品对应的历史销售量确定所述商品的日均销售量；
根据所述商品的库存情况确定所述商品的库存量，并利用所述商品的日均销售量确定所述商品的可用销售时长；
基于所述商品的可用销售时长确定所述商品的出库处理时长。
其他特征和优点将在随后的说明书中阐述，本发明的目的和其他优点在说明书以及附图中所特别指出的结构来实现和获得。
为使本发明的上述目的、特征和优点能更明显易懂，下文特举较佳实施例，并配合所附附图，作详细说明如下。
附图说明
通过参照附图详细描述其示例实施方式，本发明的上述和其它特征及优点将变得更加明显。
图1是一种支持截单前售后的提前波次作业装置的框架图；
图2是一种支持截单前售后的提前波次作业方法的流程图；
图3是商品的出库处理时长的确定的方法的流程图；
图4是修正处理时长的确定的方法的流程图。
具体实施方式
为解决上述问题，根据本发明的一个方面，如图1所示，提供了一种支持截单前售后的提前波次作业系统，具体包括：
接单处理模块，出库管理模块，任务生成模块，拣货处理模块，仓库处理模块；
其中所述接单处理模块负责面向不同来源、不同渠道订单统一接收存储，用于后续出库、入库作业单据生成来源；
所述出库管理模块负责依托出库任务生成策略，生成相应出库任务，并包含多个出库执行功能；
所述任务生成模块负责依托于将接收到的订单转换成仓库内部通知单，由任务调度中心根据波次配置策略周期性调度待履约通知单生成提前波次任务，供仓库人员获取单据进行仓库作业；
所述拣货处理模块负责将仓库提前波次生成的出库任务，根据仓库事先配置好的库区规则生成具体的仓库拣货任务供作业人员实施拣货；
所述仓库处理模块负责依托于出库时长内的订单售后明细、异常通知单明细、提前波次任务明细，交叉比对已作业部分和已发起售后部分明细，生成多出入库任务，供仓库感知需拦截、反向入库部分，反向入库完成库存平冲。
进一步的，所述出库执行功能包括仓库库存锁定、拣货任务生成、确认出库、完成出库。
具体的，还包括售后作业补偿模块，依托于订单中心履约推送售后单到仓储系统统一接单服务转换生成仓库内部异常通知单，反向生成多出入库任务，在仓库出库完成交接前推送多出入库指令，拦截已在作业的已发起售后的出库实物部分，反向入库完成库存平冲。
本实施例提供的仓储系统，包括提前波次配置单元、截单配置单元、统一接单服务单元、出库管理单元、入库管理单元、统一任务中心单元、商品信息单元；
提前波次配置单元包括不同仓库根据自营作业用力情况自由配置仓库波次出库任务生成节点，可基于上游待履约订单根据所配置生成节点触发生成波次出库任务；
截单配置单元包括不同仓库根据履约时效性自由配置仓库截单时间节点，可基于上游待履约订单根据所配置截单任务生成节点触发生成截单出库任务；
统一接单服务单元包括面向不同来源、不同渠道订单统一接收存储，用于系统自动生成策略或人工干预生成相应的出库任务；
出库管理单元包括仓库库存锁定、拣货任务生成、确认出库、完成出库等出库执行功能；
入库管理单元包括仓库收货、上架任务生成、确认入库、完成入库等入库执行功能；
统一任务中心单元包括统一任务模型支撑不同业务领域生成的作业任务，涉及任务生成、领取、提交、完成等功能；
商品信息单元包括商品基础信息的维护功能；
步骤1
根据仓库的物理位置、库位的高度、库位的商品特性、商品的包装规格、商品的出货量等因素，将仓库划分成多个库区、库位；本实施例中仓库W分成多个库区，每个库区下又规划多个库位，并设置不同库位的拣货优先级，用于仓库PDA指引拣货任务执行拣货；
步骤2
配置商品信息：在商品入库时，根据商品的包装规格，提交商品的基本信息，所述基本信息包括长、宽、高、重量、规格系数、计量单位、商品条码；本实施例中配置仓库W商品存在S1、S2、S3；
步骤3
提前波次配置：根据自营仓作业用力情况自由配置仓库提前波次出库任务生成节点，本实施例中配置仓库W第一波次出库任务生成时间为T1；
步骤4
截单配置：根据履约时效性自由配置仓库截单时间节点
本实施例中配置仓库W截单出库任务生成时间为T2，截单任务生成时间大于提前波次任务生成时间，T2>T1；
步骤5
统一接单服务：面向不同来源、不同渠道订单统一接收存储
本实施例中在提前波次任务生成节点前接收订单O1、O2、O3，其中订单O1包含仓库库内实物S1*数量Q1，订单O2包含仓库库内实物S2*数量Q2，订单O3包含仓库库内实物S3*数量Q3，统一接单服务生成通知单N1、N2、N3，其中通知单N1包含仓库库内实物S1*数量Q1，通知单N2包含仓库库内实物S2*数量Q2，通知单N3包含仓库库内实物S3*数量Q3；
步骤6
出库管理：根据统一接单服务存储订单依托出库任务生成策略，生成相应出库任务，并包含仓库库存锁定、拣货任务生成、确认出库、完成出库等出库执行功能；
本实施例中根据统一接单服务生成通知单N1、N2、N3生成出库任务C1、C2、C3，其中出库任务C1包含仓库库内实物S1*数量Q1，出库任务C2包含仓库库内实物S2*数量Q2，出库任务C3包含仓库库内实物S3*数量Q3；
步骤7
统一任务中心：统一任务模型支撑不同业务领域生成的作业任务，涉及任务生成、领取、提交、完成等功能；本实施例中根据出库任务C1、C2、C3生成统一拣货任务J1，其中拣货明细包含仓库库内实物S1*数量Q1、实物S2*数量Q2、实物S3*数量Q3;
步骤8
仓库根据拣货任务相应指引执行库内拣货任务作业并进行任务提交，将具体拣货任务J1拣货结果实物S1*数量Q1、实物S2*数量Q2、实物S3*数量Q3回传到出库任务C1、C2、C3;
步骤9
在截单时间节点T2前，订单O1发生售后退单，涉及订单明细实物S1*数量Q1，推送到仓储统一接单服务生成异常通知单AN1，包含仓库库内实物S1*数量Q1
步骤10
入库管理：根据入库任务进行相应实物入库动作，满足实物售卖库存来源，包括仓库收货、上架任务生成、确认入库、完成入库等入库执行功能；本实施例中根据售后订单产生的异常通知单AN1生成多出入库任务R1，包含仓库库内实物S1*数量Q1
步骤11
仓库库内此时拣货任务J1、出库任务C1、C2、C3均已执行完成作业，需根据多出入库任务R1进行拦截作业，将因售后拣货任务J1、出库任务C1多拣部分实物S1*数量Q1反向入库，进行实物库存平冲，达到售后库存可再售卖、满足保证仓库整体出库时效的效果。
另一方面，如图2所示，本申请提供一种支持截单前售后的提前波次作业方法，应用于上述的一种支持截单前售后的提前波次作业系统，具体包括：
S1实时获取订单对应的商品，并以所述商品对应的历史销售量以及所述商品的库存情况，确定所述商品的出库处理时长；
S2将存在所述商品的订单作为匹配商品订单以及匹配用户，确定所述商品与所述匹配用户的历史购物数据的匹配情况，利用所述匹配情况确定所述商品与匹配用户的匹配系数，利用所述匹配系数确定所述商品存在匹配偏差用户时，进入下一步骤；
S3基于与不同的匹配偏差用户的匹配系数，确定所述商品的匹配偏差系数存在异常时，进入下一步骤；
S4获取仓库在预设时段内的出库数据，并结合所述匹配偏差系数对所述出库处理时长进行修正得到修正处理时长，根据修正处理时长进行所述商品的出库时长的确定，利用所述出库时长内的售后数据进行提前拦截，并对所述商品进行库存更新。
进一步的，所述商品对应的历史销售量根据所述商品在购物平台的销售数据的解析结果进行确定。
可选的，所述商品的库存情况根据所述商品的库存量进行确定，具体的根据所述仓库的库存系统的解析结果进行确定。
需要说明的是，如图3所示，所述商品的出库处理时长的确定的方法为：
以所述商品对应的历史销售量确定所述商品的日均销售量；
根据所述商品的库存情况确定所述商品的库存量，并利用所述商品的日均销售量确定所述商品的可用销售时长；
基于所述商品的可用销售时长确定所述商品的出库处理时长。
进一步的，基于所述商品的可用销售时长确定所述商品的出库处理时长，具体包括：
基于所述商品的可用销售时长，确定所述商品的可用销售时长对应的预设处理时长，并利用所述预设处理时长确定所述商品的出库处理时长。
在另外的一个实施例中，所述商品的出库处理时长的确定的方法为：
根据所述商品的库存情况确定所述商品的库存量，当所述商品的库存量小于预设库存量时，则确定利用预设时长确定所述商品的出库处理时长；
当所述商品的库存量不小于预设库存量时：
以所述商品的库存量确定所述商品的库存量在预设库存量区间内时：
以所述商品对应的历史销售量确定所述商品在不同日期的历史销售量，当历史销售量大于预设销量阈值的日期数量不满足要求时，则确定利用预设时长确定所述商品的出库处理时长；
当历史销售量大于预设销量阈值的日期数量满足要求时：
以不同日期的历史销售量确定不同日期的历史销售量的平均值，当不同日期的历史销售量的平均值大于预设销售量阈值时：
则确定利用预设时长确定所述商品的出库处理时长；
当所述商品的库存量不在预设库存量区间内或者当不同日期的历史销售量的平均值不大于预设销售量阈值时：
利用所述商品在最近的预设时段内不同日期的销售量确定所述商品的库存量在最近的预设时段内的最短销售时长，当所述最短销售时长不满足要求时，则确定利用预设时长确定所述商品的出库处理时长；
当所述最短销售时长满足要求时：
根据所述商品的库存情况确定所述商品的库存量，并结合在不同日期的销售量确定所述商品的可用销售系数，基于所述商品的可用销售系数确定所述商品的出库处理时长。
进一步的，所述匹配用户为匹配商品订单对应的用户。
具体的，所述商品与匹配用户的匹配系数的确定的方法为：
以所述匹配用户的历史订单数据确定所述匹配用户与不同的预设画像的画像匹配系数，并利用所述画像匹配系数确定所述预设画像中的匹配用户画像；
确定所述商品与所述匹配用户画像的商品匹配系数；
根据所述商品匹配系数与画像匹配系数确定所述商品与匹配用户的匹配系数。
进一步的，所述商品匹配系数根据所述商品与所述匹配用户画像的对应的预设匹配情况进行确定。
可选的，所述商品与匹配用户的匹配系数根据所述商品匹配系数与画像匹配系数的乘积进行确定。
需要说明的是，所述商品与匹配用户的匹配系数的取值范围在0到1之间，其中当所述商品与匹配用户的匹配系数小于预设匹配系数时，则确定所述匹配用户属于匹配偏差用户。
在另外一个实施例中，所述商品与匹配用户的匹配系数的确定的方法为：
以所述匹配用户的历史订单数据确定所述匹配用户的历史购买商品，当所述历史购买商品中存在所述商品时，则确定所述匹配用户不属于匹配偏差用户；
当所述历史购买商品中不存在所述商品时：
以所述历史购买商品与所述商品的关联情况，确定所述历史购买商品与所述商品的商品关联系数，当存在商品关联系数大于预设关联系数的历史购买商品时：
将商品关联系数大于预设关联系数的历史购买商品作为关联商品，当关联商品的数量大于预设关联商品数量时，则确定所述匹配用户不属于匹配偏差用户；
当关联商品的数量不大于预设关联商品数量时：
以所述关联商品的数量以及不同的关联商品的商品关联系数确定综合关联性系数，当所述综合关联系数满足要求时，则确定所述匹配用户不属于匹配偏差用户；
当不存在商品关联系数大于预设关联系数的历史购买商品或者综合关联系数不满足要求时：
以所述匹配用户的历史订单数据确定所述匹配用户与不同的预设画像的画像匹配系数，并利用所述画像匹配系数确定所述预设画像中的匹配用户画像，当存在与所述商品的商品匹配系数大于预设匹配系数的匹配用户画像时：
当匹配用户画像与所述商品的商品匹配系数的和满足要求时，则确定所述匹配用户不属于匹配偏差用户；
当不存在与所述商品的商品匹配系数大于预设匹配系数的所述匹配用户画像时：
确定所述商品与所述匹配用户画像的商品匹配系数，根据与不同的预设画像的商品匹配系数与画像匹配系数确定所述商品与匹配用户的匹配系数。
进一步的，确定所述商品的匹配偏差系数存在异常，具体包括：
以与不同的匹配偏差用户的匹配系数为基础，确定不同的匹配偏差用户的权重系数；
根据不同的匹配偏差用户的权重系数确定所述商品的匹配偏差系数，并结合所述预设系数阈值确定所述商品的匹配偏差系数是否存在异常。
需要说明的是，当所述商品的匹配偏差系数小于预设系数阈值时，则确定所述商品的匹配偏差系数存在异常。
具体的，如图4所示，所述修正处理时长的确定的方法为：
以所述仓库在预设时段内的出库数据，确定所述仓库在预设时段内不同的单位时段内的出库量，并利用在不同的单位时段内的出库量确定出库时长修正因子；
基于所述出库时长修正因子、匹配偏差系数对所述出库处理时长进行修正得到修正处理时长。
进一步的，对所述商品进行库存更新，具体包括：
对所述商品在尚未出库时进行拦截，并利用拦截处理结果对所述商品进行库存更新。
本说明书中的各个实施例均采用递进的方式描述，各个实施例之间相同相似的部分互相参见即可，每个实施例重点说明的都是与其他实施例的不同之处。尤其，对于装置、设备、非易失性计算机存储介质实施例而言，由于其基本相似于方法实施例，所以描述的比较简单，相关之处参见方法实施例的部分说明即可。
上述对本说明书特定实施例进行了描述。其它实施例在所附权利要求书的范围内。在一些情况下，在权利要求书中记载的动作或步骤可以按照不同于实施例中的顺序来执行并且仍然可以实现期望的结果。另外，在附图中描绘的过程不一定要求示出的特定顺序或者连续顺序才能实现期望的结果。在某些实施方式中，多任务处理和并行处理也是可以的或者可能是有利的。
以上所述仅为本说明书的一个或多个实施例而已，并不用于限制本说明书。对于本领域技术人员来说，本说明书的一个或多个实施例可以有各种更改和变化。凡在本说明书的一个或多个实施例的精神和原理之内所作的任何修改、等同替换、改进等，均应包含在本说明书的权利要求范围之内。

