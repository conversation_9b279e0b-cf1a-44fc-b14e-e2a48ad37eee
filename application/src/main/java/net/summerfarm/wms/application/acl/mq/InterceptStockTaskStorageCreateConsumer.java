package net.summerfarm.wms.application.acl.mq;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.instore.WmsStockTaskStorageNoticeOrderCommandService;
import net.summerfarm.wms.application.instore.assembler.WmsStockTaskStorageNoticeOrderAssembler;
import net.summerfarm.wms.application.instore.event.InterceptStockTaskStorageCreateEvent;
import net.summerfarm.wms.application.instore.input.StockTaskStorageCreateByNoticeCommandInput;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.RedisKeys;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.RedisUtil;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.util.MessageExtUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/20
 */
@Slf4j
@Component
@MqListener(topic = Global.MQ_TOPIC,
        tag = WmsConstant.INTERCEPT_STOCK_TASK_STORAGE_CREATE,
        consumerGroup = WmsConstant.MQ_WMS_GID,
        maxReconsumeTimes = 5)
public class InterceptStockTaskStorageCreateConsumer extends AbstractMqListener<InterceptStockTaskStorageCreateEvent> {

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WmsStockTaskStorageNoticeOrderCommandService wmsStockTaskStorageNoticeOrderCommandService;

    @Override
    public void process(InterceptStockTaskStorageCreateEvent interceptStockTaskStorageCreateEvent) {
        log.info("接收创建拦截入库任务入参：{}", JSON.toJSONString(interceptStockTaskStorageCreateEvent));
        if (null == interceptStockTaskStorageCreateEvent || StringUtils.isBlank(interceptStockTaskStorageCreateEvent.getGoodsRecycleOrderNo())) {
            return;
        }
        redisUtil.mqOnceLockExecute(MessageExtUtil.getMessageExt().getMsgId(),
                () -> {
                    String key = RedisKeys.buildInterceptStockTaskStorageCreateConsumeExKey(interceptStockTaskStorageCreateEvent.getGoodsRecycleOrderNo());
                    redisUtil.doLock(key, 5L, TimeUnit.MINUTES, 0L, () -> {
                        StockTaskStorageCreateByNoticeCommandInput stockTaskStorageCreateByNoticeCommandInput = WmsStockTaskStorageNoticeOrderAssembler.buildCreateByNoticeInput(interceptStockTaskStorageCreateEvent);
                        wmsStockTaskStorageNoticeOrderCommandService.createStockTaskStorage(stockTaskStorageCreateByNoticeCommandInput);
                    });
                });
    }

}
