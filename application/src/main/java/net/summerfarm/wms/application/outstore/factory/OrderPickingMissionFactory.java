package net.summerfarm.wms.application.outstore.factory;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.wms.api.h5.outstore.req.OrderPickingCommitCommand;
import net.summerfarm.wms.api.h5.outstore.req.OrderPickingCreateCommand;
import net.summerfarm.wms.api.h5.outstore.req.OrderPickingCreateDetailDTO;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.domain.common.domainobject.Container;
import net.summerfarm.wms.domain.common.repository.ContainerRepository;
import net.summerfarm.wms.domain.mission.domainobject.*;
import net.summerfarm.wms.domain.mission.enums.*;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.summerfarm.wms.domain.mission.util.MissionNoFactory;
import net.summerfarm.wms.domain.sku.domainobject.Sku;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class OrderPickingMissionFactory {

    @Resource
    private MissionNoFactory missionNoFactory;

    @Resource
    private ContainerRepository containerRepository;

    @Resource
    private MissionRepository missionRepository;

    /**
     * 构建拣货提交对象
     *
     * @param command req
     * @return mission
     */
    public Mission createPickingCommit(OrderPickingCommitCommand command) {
        Mission mission = missionRepository.findMission(command.getMissionNo());

        List<ExecuteUnit> executeUnits = mission.getExecuteUnits();
        Map<String, String> unitNoMap = executeUnits.stream().collect(Collectors.toMap(ExecuteUnit::getCarrierCode, ExecuteUnit::getUnitNo, (o1, o2) -> o1));

        List<MissionDetail> missionDetails = mission.getMissionDetails();
        Set<String> dupKeyMap = missionDetails.stream().map(MissionDetail::duplicateKey).collect(Collectors.toSet());

        // 组装提交明细
        List<SubmitDetail> submitDetails = Optional.ofNullable(command.getDetails()).orElse(Lists.newArrayList()).stream()
                .map(detail -> {
                    if (StringUtils.isEmpty(detail.getCabinetNo()) || StringUtils.isEmpty(detail.getContainerNo())) {
                        throw new BizException("库位和容器不可为空");
                    }
                    return SubmitDetail.builder()
                            .tenantId(command.getTenantId())
                            .targetCarrierCode(detail.getContainerNo().trim())
                            .targetCarrierType(MissionCarrierTypeEnum.CONTAINER.getCode())
                            .sku(detail.getSku())
                            .sourceCarrierType(MissionCarrierTypeEnum.CABINET.getCode())
                            .sourceCarrierCode(detail.getCabinetNo().trim())
                            .operatorId(command.getOperatorId())
                            .shelfLife(detail.getQualityDate())
                            .missionType(mission.getMissionType())
                            .produceTime(detail.getProduceDate())
                            .missionNo(mission.getMissionNo())
                            .warehouseNo(mission.getWarehouseNo())
                            .quantity(detail.getQuantity())
                            .opreatorName(command.getOperator())
                            .unitNo(unitNoMap.get(detail.getCabinetNo()))
                            .build();
                }).collect(Collectors.toList());

        // 容器校验
        List<String> containerNos = submitDetails.stream().map(SubmitDetail::getTargetCarrierCode).distinct().collect(Collectors.toList());
        List<Container> containers = containerRepository.listByContainerNos(command.getWarehouseNo(), containerNos);
        ExceptionUtil.checkAndThrowV2(CollectionUtils.isNotEmpty(containers) &&
                containers.size() != containerNos.size(), "容器号不存在");

        // 提交库位校验
        ExceptionUtil.checkAndThrowV2(CollectionUtils.isNotEmpty(submitDetails) &&
                        !submitDetails.stream().map(SubmitDetail::duplicateKey).allMatch(dupKeyMap::contains),
                "请检查扫描库位与任务是否一致");

        // 组装异常提交明细
        List<AbnormalSubmitDetail> abnormalSubmitDetails = Optional.ofNullable(command.getAbnormalDetails())
                .orElse(Lists.newArrayList())
                .stream()
                .map(detail -> AbnormalSubmitDetail.builder()
                        .abnormalQuantity(detail.getAbnormalQuantity())
                        .missionType(mission.getMissionType())
                        .abnormalReason(detail.getAbnormalReason())
                        .missionNo(mission.getMissionNo())
                        .sku(detail.getSku())
                        .sourceCarrierType(MissionCarrierTypeEnum.CABINET.getCode())
                        .operatorId(command.getOperatorId())
                        .produceTime(detail.getProduceDate())
                        .shelfLife(detail.getQualityDate())
                        .opreatorName(command.getOperator())
                        .sourceCarrierCode(detail.getCabinetNo().trim())
                        .warehouseNo(mission.getWarehouseNo())
                        .unitNo(unitNoMap.get(detail.getCabinetNo().trim()))
                        .tenantId(command.getTenantId())
                        .build()).collect(Collectors.toList());

        // 提交库位校验
        ExceptionUtil.checkAndThrowV2(CollectionUtils.isNotEmpty(abnormalSubmitDetails) &&
                        !abnormalSubmitDetails.stream().map(AbnormalSubmitDetail::duplicateKey).allMatch(dupKeyMap::contains),
                "请检查扫描库位与任务是否一致");

        mission.setSubmitDetails(submitDetails);
        mission.setAbnormalSubmitDetails(abnormalSubmitDetails);
        return mission;
    }

    /**
     * 构建拣货提交对象
     *
     * @param command req
     * @return mission
     */
    public List<Mission> createOrderPicking(OrderPickingCreateCommand command) {
        ArrayList<Mission> res = Lists.newArrayList();
        Map<String, List<OrderPickingCreateDetailDTO>> zoneDetailMap = command.getDetails().stream().collect(Collectors.groupingBy(OrderPickingCreateDetailDTO::getZone));
        zoneDetailMap.forEach((zone, details) -> {
            String missionNo = missionNoFactory.createMissionNo(MissionTypeEnum.ORDER_PICKING.getCode());
            Mission mission = Mission.builder()
                    .warehouseNo(command.getWarehouseNo())
                    .tenantId(1L)
                    .creatorName(command.getCreator())
                    .creatorId(command.getCreatorId())
                    .state(MissionStateEnum.WAIT_ASSIGN.getCode())
                    .missionNo(missionNo)
                    .cancelTime(0L)
                    .missionType(MissionTypeEnum.ORDER_PICKING.getCode())
                    .build();

            List<MissionSourceProperty> sourceProperties = details.stream().map(item ->
                    MissionSourceProperty.builder()
                            .missionNo(missionNo)
                            .missionType(MissionTypeEnum.ORDER_PICKING.getCode())
                            .sourceOrderNo(item.getSourceOrderNo())
                            .warehouseNo(command.getWarehouseNo())
                            .sourceId(item.getSourceId())
                            .sourceType(item.getSourceType())
                            .storeNo(command.getStoreNo())
                            .targetWarehouseNo(item.getTargetWarehouseNo())
                            .expectTime(item.getExpectTime())
                            .businessTag(command.getBusinessTag())
                            .build()
            ).distinct().collect(Collectors.toList());

            List<ExecuteUnit> units = details.stream().map(OrderPickingCreateDetailDTO::getCabinetNo).distinct().map(cabinetNo ->
                    ExecuteUnit.builder()
                            .carrierType(MissionCarrierTypeEnum.CABINET.getCode())
                            .carrierCode(cabinetNo)
                            .missionNo(missionNo)
                            .state(ExecuteUnitStateEnum.EXE.getCode())
                            .missionType(MissionTypeEnum.ORDER_PICKING.getCode())
                            .cancelTime(0L)
                            .tenantId(1L)
                            .unitNo(missionNoFactory.createUnitNo())
                            .warehouseNo(command.getWarehouseNo())
                            .build()).collect(Collectors.toList());
            Map<String, String> unitNoMap = units.stream().collect(Collectors.toMap(ExecuteUnit::getCarrierCode, ExecuteUnit::getUnitNo, (o1, o2) -> o1));

            List<MissionDetail> saveDetail = Lists.newArrayList();
            details.stream().collect(Collectors.groupingBy(OrderPickingCreateDetailDTO::dupKey))
                    .forEach((key, list) -> {
                        OrderPickingCreateDetailDTO item = list.get(NumberUtils.INTEGER_ZERO);
                        Sku skuInfo = Sku.builder()
                                .sku(item.getSku())
                                .pdName(item.getPdName())
                                .skuType(item.getSkuType())
                                .origin(item.getOrigin())
                                .cargoType(Objects.equals(4, item.getCategoryType()) ? "鲜果" : "标品")
                                .packaging(item.getPackaging())
                                .specification(item.getSpecification())
                                .temperature(item.getTemperature())
                                .build();
                        MissionDetailExtend extend = MissionDetailExtend.builder()
                                .missionNo(mission.getMissionNo())
                                .sku(item.getSku())
                                .extend(JSON.toJSONString(skuInfo))
                                .warehouseNo(mission.getWarehouseNo())
                                .build();
                        MissionDetail build = MissionDetail.builder()
                                .shouldInQuantity(list.stream().mapToInt(OrderPickingCreateDetailDTO::getQuantity).sum())
                                .warehouseNo(mission.getWarehouseNo())
                                .cabinetNo(item.getCabinetNo())
                                .sku(item.getSku())
                                .unitNo(unitNoMap.get(item.getCabinetNo()))
                                .shelfLife(item.getQualityDate())
                                .produceTime(item.getProduceDate())
                                .missionNo(mission.getMissionNo())
                                .exeQuantity(0)
                                .abnormalQuantity(0)
                                .extend(extend)
                                .state(MissionDetailStateEnum.INIT.getCode())
                                .build();

                        saveDetail.add(build);
                    });


            mission.setExecuteUnits(units);
            mission.setSourceProperty(sourceProperties);
            mission.setMissionDetails(saveDetail);
            res.add(mission);
        });


        return res;
    }
}
