package net.summerfarm.wms.infrastructure.dao.materialManage;


import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingDetailQueryParam;
import net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBindingDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Mapper
public interface WmsMaterialBindingDetailMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WmsMaterialBindingDetail record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WmsMaterialBindingDetail record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WmsMaterialBindingDetail selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<WmsMaterialBindingDetail> selectByCondition(WmsMaterialBindingDetailQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<WmsMaterialBindingDetailEntity> getPage(WmsMaterialBindingDetailQueryParam param);

    /**
     * 批量写入
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<WmsMaterialBindingDetail> list);

    void updateDeletedByBindingId(@Param("bindingId") Long bindingId, @Param("updater") String updater);
}

