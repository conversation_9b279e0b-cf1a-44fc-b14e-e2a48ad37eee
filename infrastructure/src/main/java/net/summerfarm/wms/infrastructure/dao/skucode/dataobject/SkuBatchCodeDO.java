package net.summerfarm.wms.infrastructure.dao.skucode.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/12/10  15:51
 * sku 批次 生产日期 唯一码
 */
@Data
@Builder
@AllArgsConstructor
public class SkuBatchCodeDO implements Serializable {

    private Integer id;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;

    /**
    * sku
    */
    private String sku;

    /**
    * 批次
    */
    private String purchaseNo;

    /**
     * 批次类型，1：降级批次，2：临保批次
     */
    private Integer batchType;

    /**
     * 业务id:如入库单id
     */
    private Long bizId;

    private Integer bizType;

    /**
    * 生产日期
    */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productionDate;

    /**
    * 保质期
    */
    private LocalDate qualityDate;

    /**
    * 唯一码 批次唯一码 根据id生成前方补0 共10位 1位分隔符 "S"
    */
    private String skuBatchOnlyCode;

    /**
    * 打印次数
    */
    private Integer printNumber;

    public SkuBatchCodeDO(){}

    public SkuBatchCodeDO(String sku, String purchaseNo, LocalDate productionDate){
        this.sku = sku;
        this.purchaseNo = purchaseNo;
        this.productionDate = productionDate;
    }


}
