package net.summerfarm.wms.infrastructure.dao.batch.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description
 * @Date 2025/7/30 14:59
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseCostBatchRelationDO {

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 采购批次号
     */
    private String purchaseNo;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 原采购批次号
     */
    private String originPurchaseNo;

}
