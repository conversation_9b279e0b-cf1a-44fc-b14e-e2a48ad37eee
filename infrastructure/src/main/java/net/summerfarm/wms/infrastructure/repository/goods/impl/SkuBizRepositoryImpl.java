package net.summerfarm.wms.infrastructure.repository.goods.impl;

import com.google.common.collect.Lists;
import net.summerfarm.wms.domain.sku.SkuBizRepository;
import net.summerfarm.wms.domain.sku.domainobject.SkuBiz;
import net.summerfarm.wms.infrastructure.dao.goods.WmsSkuBizDAO;
import net.summerfarm.wms.infrastructure.dao.goods.dataobject.WmsSkuBizDO;
import net.summerfarm.wms.infrastructure.repository.goods.converter.SkuBizConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/05/20
 * sku biz repository
 */
@Component
public class SkuBizRepositoryImpl implements SkuBizRepository {

    @Resource
    private WmsSkuBizDAO wmsSkuBizDAO;

    /**
     * 插入skuBiz
     * @param skuBiz 插入对象
     * @return 影响行数
     */
    @Override
    public Integer insertSelective(SkuBiz skuBiz) {
        return wmsSkuBizDAO.insertSelective(SkuBizConverter.INSTANCE.convert(skuBiz));
    }

    /**
     * 更新skuBiz
     *
     * @param skuBiz 更新对象
     * @return 影响行数
     */
    @Override
    public Long update(SkuBiz skuBiz) {
        return wmsSkuBizDAO.update(SkuBizConverter.INSTANCE.convert(skuBiz));
    }

    @Override
    public List<SkuBiz> findSkuBiz(Long warehouseNo, List<String> skus) {
        if (CollectionUtils.isEmpty(skus)){
            return Lists.newArrayList();
        }
        List<WmsSkuBizDO> wmsSkuBizDOS = wmsSkuBizDAO.selectBySkus(warehouseNo, skus);
        return wmsSkuBizDOS.stream().map(SkuBizConverter.INSTANCE::convert).collect(Collectors.toList());
    }
}
