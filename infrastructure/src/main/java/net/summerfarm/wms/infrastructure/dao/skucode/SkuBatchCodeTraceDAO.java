package net.summerfarm.wms.infrastructure.dao.skucode;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeTrace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 批次唯一溯源码 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Mapper
public interface SkuBatchCodeTraceDAO extends BaseMapper<SkuBatchCodeTrace> {

    int updateWeight(@Param("id") Long id,
                     @Param("weight") BigDecimal weight,
                     @Param("state") Integer state,
                     @Param("weightPerson") String weightPerson,
                     @Param("weightTime") LocalDateTime weightTime);

    List<SkuBatchCodeTraceEntity> findDeliveryTimeRecentlyByMerchantId(@Param("merchantIds") List<String> merchantIds, @Param("deliveryTime") LocalDate deliveryTime);

    List<SkuBatchCodeTraceEntity> findDeliveryTimeFirstByMerchantId(@Param("merchantIds") List<String> merchantIds);

}
